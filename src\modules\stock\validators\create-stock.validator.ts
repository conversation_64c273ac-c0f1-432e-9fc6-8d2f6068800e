import { z } from "zod";
import { NcmFormatterUtil } from "../utils/ncm-formatter.util";

const numberPreprocess = (isOptional = true, isInteger = false) =>
	z.preprocess(
		val => {
			if (typeof val === "string") {
				const parsed = isInteger ? parseInt(val) : parseFloat(val);
				return isNaN(parsed) ? undefined : parsed;
			}
			if (typeof val === "number") {
				return isNaN(val) ? undefined : isInteger ? Math.floor(val) : val;
			}
			return val;
		},
		isOptional ? z.number().optional() : z.number().min(0, "O valor deve ser maior ou igual a zero")
	);

export const supplierSchema = z.object({
	id: z.number(),
	name: z.string(),
});

export const invoiceSchema = z.object({
	key: z.string(),
	issueDate: z.string(),
	supplier: supplierSchema,
});

export const packageSchema = z
	.object({
		name: z.string().optional(),
		barcode: z.string().optional(),
		code: z.string().optional(),
		quantityPerPackage: numberPreprocess(true, true).optional(),
		id: z.number().optional(),
	})
	.refine(
		data => {
			if (data.id) return true;
			const stringFields = [data.name, data.barcode, data.code];
			const hasAnyField = stringFields.some(field => field?.trim()) || data.quantityPerPackage;
			if (!hasAnyField) return true;
			const allStringFieldsValid = stringFields.every(field => field?.trim());
			return allStringFieldsValid && data.quantityPerPackage && data.quantityPerPackage > 0;
		},
		{
			message: "Se algum campo da caixa for preenchido, todos os campos se tornam obrigatórios",
			path: ["name"],
		}
	);

const pricePreprocess = z.preprocess(
	val => {
		if (typeof val === "string") {
			const parsed = parseFloat(val);
			return isNaN(parsed) ? undefined : parsed;
		}
		if (typeof val === "number") {
			return isNaN(val) ? undefined : val;
		}
		return val;
	},
	z.union([z.number().min(0, "O preço deve ser maior ou igual a zero"), z.undefined(), z.null()]).optional()
);

const productBaseObject = z.object({
	id: z.number().optional(),
	name: z.string().min(3, "O nome do produto é obrigatório e deve ter no mínimo 3 caracteres"),
	barcode: z.string().min(1, "O código de barras é obrigatório"),
	supplierCode: z.string().optional(),
	price: pricePreprocess.refine(v => typeof v === "number" && v > 0, { message: "O preço de venda é obrigatório e deve ser maior que zero" }),
	costPrice: pricePreprocess.refine(v => typeof v === "number" && v > 0, { message: "O preço de custo é obrigatório e deve ser maior que zero" }),
	ncm: z.string().min(1, "O NCM é obrigatório"),
	// .refine(value => NcmFormatterUtil.isValidNcm(value), "O NCM deve ter 8 dígitos numéricos"),
});

export const productBaseSchema = productBaseObject;
export const productDetailsSchema = productBaseObject.extend({
	description: z.string().optional(),
	categoryId: z.number().optional(),
	package: packageSchema.optional(),
});

export const stockMovementSchema = z.object({
	description: z.string().optional(),
	quantity: numberPreprocess(false).refine(v => typeof v === "number" && v > 0, {
		message: "A quantidade é obrigatória e deve ser maior que zero",
	}),
	product: productDetailsSchema,
	packageQuantity: numberPreprocess(),
});

export const inventorySchema = z.object({
	id: z.number().optional(),
	expirationDate: z.string().optional(),
	stockMovement: stockMovementSchema,
});

export const createStockDtoSchema = z.object({
	invoice: invoiceSchema,
	inventories: z.array(inventorySchema),
});

export type ICreateStock = z.infer<typeof createStockDtoSchema>;
